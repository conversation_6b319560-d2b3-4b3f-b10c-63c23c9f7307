'use client';

export default function EnvTestPage() {
  // Log all available environment variables
  console.log('All process.env:', process.env);
  
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN', 
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID',
    'NEXT_PUBLIC_FIREBASE_VAPID_KEY',
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_APP_VERSION',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_REQUIRE_2FA',
    'NEXT_PUBLIC_SESSION_TIMEOUT',
    'NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS',
    'NEXT_PUBLIC_DEBUG_MODE',
    'NEXT_PUBLIC_SHOW_DEV_TOOLS',
    'NEXT_PUBLIC_ENABLE_USER_MANAGEMENT',
    'NEXT_PUBLIC_ENABLE_ANALYTICS_DASHBOARD',
    'NEXT_PUBLIC_ENABLE_CONTENT_MANAGEMENT',
    'NEXT_PUBLIC_ENABLE_CAMPAIGN_MANAGEMENT',
    'NEXT_PUBLIC_ADMIN_API_BASE_URL',
    'NODE_ENV'
  ];

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Variables Test</h1>
      <div className="space-y-2">
        {requiredVars.map((varName) => {
          const value = process.env[varName];
          const isAvailable = value !== undefined;
          
          return (
            <div key={varName} className="flex items-center space-x-2">
              <span className={`w-4 h-4 rounded-full ${isAvailable ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span className="font-mono text-sm w-80">{varName}:</span>
              <span className="font-mono text-sm">
                {isAvailable ? (
                  <span className="text-green-600">"{value}"</span>
                ) : (
                  <span className="text-red-600">undefined</span>
                )}
              </span>
            </div>
          );
        })}
      </div>
      
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Test Environment Validation</h2>
        <button 
          onClick={() => {
            try {
              import('@/lib/env').then(({ env }) => {
                console.log('✅ Environment validation successful:', env);
                alert('Environment validation successful!');
              }).catch((error) => {
                console.error('❌ Environment validation failed:', error);
                alert(`Environment validation failed: ${error.message}`);
              });
            } catch (error) {
              console.error('❌ Import failed:', error);
              alert(`Import failed: ${error}`);
            }
          }}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Validation
        </button>
      </div>
    </div>
  );
}
