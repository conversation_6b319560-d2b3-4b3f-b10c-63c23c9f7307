/**
 * CREATE JOHN ADMIN USER
 * 
 * Creates <EMAIL> admin user in both Firebase Auth and Firestore
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Initialize Firebase
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
  private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
  client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
});

const db = admin.firestore();
const auth = admin.auth();

async function createJohnAdmin() {
  try {
    console.log('🚀 Creating John admin user...');
    
    const email = '<EMAIL>';
    const password = '@Iamachessgrandmaster23';
    const name = '<PERSON>';
    
    // Create user in Firebase Auth
    let userRecord;
    try {
      userRecord = await auth.createUser({
        email: email,
        password: password,
        displayName: name,
        emailVerified: true
      });
      console.log('✅ Created Firebase Auth user');
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        userRecord = await auth.getUserByEmail(email);
        await auth.updateUser(userRecord.uid, {
          password: password,
          displayName: name,
          emailVerified: true
        });
        console.log('✅ Updated existing Firebase Auth user');
      } else {
        throw error;
      }
    }

    // Set custom claims for admin
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin',
      permissions: ['*']
    });
    console.log('✅ Set admin custom claims');

    // Create user document in Firestore
    const userData = {
      id: userRecord.uid,
      email: email,
      name: name,
      firstName: 'John',
      lastName: 'Admin',
      
      // WordPress-style admin role
      role: 'admin',
      roleRef: 'roles/admin',
      roleName: 'Administrator',
      permissions: ['*'],
      level: 100,
      
      // Profile data
      profile: {
        title: 'System Administrator',
        bio: 'Chess grandmaster and system administrator',
        avatar: null,
        department: 'IT'
      },
      
      // WordPress-style capabilities
      capabilities: {
        manageOptions: true,
        manageUsers: true,
        editThemes: true,
        editPlugins: true,
        editPosts: true,
        editPages: true,
        editOthersPages: true,
        editOthersPosts: true,
        deletePages: true,
        deletePosts: true,
        manageCategories: true,
        moderateComments: true,
        uploadFiles: true
      },
      
      // Status
      isActive: true,
      isVerified: true,
      lastLoginAt: null,
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    };

    await db.collection('users').doc(userRecord.uid).set(userData);
    console.log('✅ Created Firestore user document');

    // Update admin role user count
    await db.collection('roles').doc('admin').update({
      userCount: admin.firestore.FieldValue.increment(1)
    });

    // Create user preferences
    const preferences = {
      userId: userRecord.uid,
      userRef: `users/${userRecord.uid}`,
      
      // Admin preferences
      adminPreferences: {
        dashboardLayout: 'grid',
        showWelcomePanel: true,
        showAdvancedMenus: true,
        colorScheme: 'fresh'
      },
      
      // General preferences
      theme: 'dark', // Chess grandmaster prefers dark theme
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      
      // Notification preferences
      notifications: {
        email: true,
        push: true,
        comments: true,
        updates: true
      },
      
      // Privacy settings
      privacy: {
        profileVisible: true,
        showEmail: false,
        allowMessages: true
      },
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };
    
    await db.collection('user_preferences').doc(userRecord.uid).set(preferences);
    console.log('✅ Created user preferences');

    console.log('');
    console.log('🎉 JOHN ADMIN USER CREATED SUCCESSFULLY!');
    console.log('');
    console.log('🔐 LOGIN CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: @Iamachessgrandmaster23');
    console.log('🆔 UID:', userRecord.uid);
    console.log('👑 Role: Administrator (Full Access)');
    console.log('');
    console.log('✅ Ready for admin login!');

  } catch (error) {
    console.error('❌ Error creating John admin:', error);
  } finally {
    process.exit(0);
  }
}

createJohnAdmin();
