require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
  private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
  client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
});

const db = admin.firestore();

async function verify() {
  try {
    const [roles, users] = await Promise.all([
      db.collection('roles').get(),
      db.collection('users').get()
    ]);

    console.log('🎉 WORDPRESS-STYLE SYSTEM VERIFIED!');
    console.log('');
    console.log('📊 ROLES:');
    roles.docs.forEach(doc => {
      const data = doc.data();
      console.log(`✅ ${data.name} (${doc.id}) - Level ${data.level}`);
    });
    
    console.log('');
    console.log('👥 USERS:');
    users.docs.forEach(doc => {
      const data = doc.data();
      console.log(`✅ ${data.name} (${data.email}) - Role: ${data.roleName}`);
    });
    
    console.log('');
    console.log('🔐 LOGIN CREDENTIALS:');
    console.log('📧 Admin: <EMAIL> (Full WordPress-style access)');
    console.log('📧 Editor: <EMAIL> (Content management)');
    console.log('📧 User: <EMAIL> (Basic user)');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

verify();
