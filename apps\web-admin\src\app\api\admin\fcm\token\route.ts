import { NextRequest, NextResponse } from 'next/server';
import { getAdminDb, verifyIdToken } from '@/lib/firebase-admin';
import { Timestamp } from 'firebase-admin/firestore';

// Middleware to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header');
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await verifyIdToken(idToken);
    
    // Check if user has admin privileges (you would check custom claims in production)
    if (!decodedToken.email_verified) {
      throw new Error('Email not verified');
    }

    return decodedToken;
  } catch (error) {
    throw new Error('Unauthorized');
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const decodedToken = await verifyAdminAuth(request);

    const body = await request.json();
    const { userId, token, platform, userAgent, permissions } = body;

    // Validate required fields
    if (!userId || !token || !platform) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, token, platform' },
        { status: 400 }
      );
    }

    // Get Firebase Admin Firestore instance
    const db = getAdminDb();

    // Create admin FCM device record
    const deviceData = {
      userId,
      token,
      platform,
      browser: getBrowserFromUserAgent(userAgent),
      os: getOSFromUserAgent(userAgent),
      device: getDeviceFromUserAgent(userAgent),
      appVersion: '1.0.0',
      isActive: true,
      isAdmin: true,
      permissions: permissions || [],
      lastSeen: Timestamp.now(),
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
    };

    // Deactivate old admin tokens for this user and platform
    const oldTokensQuery = db.collection('fcm_devices')
      .where('userId', '==', userId)
      .where('platform', '==', platform)
      .where('isAdmin', '==', true);

    const oldTokensSnapshot = await oldTokensQuery.get();
    const batch = db.batch();

    oldTokensSnapshot.docs.forEach(docSnapshot => {
      batch.update(docSnapshot.ref, {
        isActive: false,
        updatedAt: Timestamp.now()
      });
    });

    // Add new admin token
    const newTokenRef = db.collection('fcm_devices').doc();
    batch.set(newTokenRef, deviceData);

    await batch.commit();

    // Update admin notification preferences
    const adminPrefsRef = db.collection('notification_preferences').doc(userId);
    const adminPrefsDoc = await adminPrefsRef.get();

    if (adminPrefsDoc.exists) {
      const currentPrefs = adminPrefsDoc.data();
      const updatedTokens = [...(currentPrefs?.fcmTokens || [])];

      // Remove old token if exists and add new one
      const tokenIndex = updatedTokens.indexOf(token);
      if (tokenIndex === -1) {
        updatedTokens.push(token);
      }

      await adminPrefsRef.update({
        fcmTokens: updatedTokens,
        topics: [...(currentPrefs?.topics || []), 'admin', 'admin-contacts', 'system-alerts'],
        updatedAt: Timestamp.now(),
      });
    } else {
      // Create admin preferences
      await adminPrefsRef.set({
        userId,
        email: decodedToken.email || '',
        fcmTokens: [token],
        topics: ['admin', 'admin-contacts', 'admin-users', 'system-alerts'],
        preferences: {
          contactForm: true,
          newsletter: false,
          userUpdates: true,
          campaigns: true,
          dailyDigest: true,
          weeklyReport: true,
          systemAlerts: true,
          marketing: false,
        },
        timezone: 'UTC',
        language: 'en',
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
        },
        isAdmin: true,
        adminLevel: 'admin', // Could be 'super-admin', 'admin', 'moderator'
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      });
    }

    // Log admin token registration
    await db.collection('admin_audit_logs').doc().set({
      action: 'fcm_token_registered',
      userId,
      adminId: decodedToken.uid,
      details: {
        platform,
        browser: deviceData.browser,
        os: deviceData.os,
      },
      timestamp: Timestamp.now(),
    });

    return NextResponse.json({
      success: true,
      message: 'Admin FCM token saved successfully',
      deviceId: newTokenRef.id,
    });

  } catch (error: any) {
    console.error('Admin FCM token save error:', error);

    if (error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to save admin FCM token' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const decodedToken = await verifyAdminAuth(request);

    const body = await request.json();
    const { userId, token } = body;

    if (!userId || !token) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, token' },
        { status: 400 }
      );
    }

    const db = getAdminDb();

    // Find and deactivate the admin token
    const tokenQuery = db.collection('fcm_devices')
      .where('userId', '==', userId)
      .where('token', '==', token)
      .where('isAdmin', '==', true);

    const tokenSnapshot = await tokenQuery.get();
    const batch = db.batch();

    tokenSnapshot.docs.forEach(docSnapshot => {
      batch.update(docSnapshot.ref, {
        isActive: false,
        updatedAt: Timestamp.now()
      });
    });

    await batch.commit();

    // Remove token from admin preferences
    const adminPrefsRef = db.collection('notification_preferences').doc(userId);
    const adminPrefsDoc = await adminPrefsRef.get();

    if (adminPrefsDoc.exists) {
      const currentPrefs = adminPrefsDoc.data();
      const updatedTokens = (currentPrefs?.fcmTokens || []).filter((t: string) => t !== token);

      await adminPrefsRef.update({
        fcmTokens: updatedTokens,
        updatedAt: Timestamp.now(),
      });
    }

    // Log admin token removal
    await db.collection('admin_audit_logs').doc().set({
      action: 'fcm_token_removed',
      userId,
      adminId: decodedToken.uid,
      details: { token: token.substring(0, 20) + '...' },
      timestamp: Timestamp.now(),
    });

    return NextResponse.json({
      success: true,
      message: 'Admin FCM token removed successfully',
    });

  } catch (error: any) {
    console.error('Admin FCM token removal error:', error);

    if (error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to remove admin FCM token' },
      { status: 500 }
    );
  }
}

// Helper functions to parse user agent
function getBrowserFromUserAgent(userAgent: string = ''): string {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
}

function getOSFromUserAgent(userAgent: string = ''): string {
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Mac OS')) return 'macOS';
  if (userAgent.includes('Linux')) return 'Linux';
  if (userAgent.includes('Android')) return 'Android';
  if (userAgent.includes('iOS')) return 'iOS';
  return 'Unknown';
}

function getDeviceFromUserAgent(userAgent: string = ''): string {
  if (userAgent.includes('Mobile')) return 'Mobile';
  if (userAgent.includes('Tablet')) return 'Tablet';
  return 'Desktop';
}
