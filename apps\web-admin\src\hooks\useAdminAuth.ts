'use client';

import { useState } from 'react';
import { signInWithEmailAndPassword, sendPasswordResetEmail } from 'firebase/auth';
import { doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { firebaseAuth, firebaseDb } from '@/lib/firebase';

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
  roleName: string;
  permissions: string[];
  isActive: boolean;
}

interface UseAdminAuthReturn {
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<AdminUser | null>;
  resetPassword: (email: string) => Promise<boolean>;
  clearError: () => void;
}

export function useAdminAuth(): UseAdminAuthReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const signIn = async (email: string, password: string): Promise<AdminUser | null> => {
    try {
      setLoading(true);
      setError(null);

      // Sign in with Firebase Auth
      const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
      const firebaseUser = userCredential.user;

      // Fetch admin user data from Firestore
      const userDoc = await getDoc(doc(firebaseDb, 'users', firebaseUser.uid));
      
      if (!userDoc.exists()) {
        throw new Error('Admin user data not found');
      }

      const userData = userDoc.data();
      
      // Verify user is admin
      if (userData.role !== 'admin') {
        await firebaseAuth.signOut(); // Sign out non-admin user
        throw new Error('Access denied: Admin privileges required');
      }

      // Verify user is active
      if (!userData.isActive) {
        await firebaseAuth.signOut();
        throw new Error('Account is disabled');
      }

      // Update last login time
      await updateDoc(doc(firebaseDb, 'users', firebaseUser.uid), {
        lastLoginAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      const adminUser: AdminUser = {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        roleName: userData.roleName,
        permissions: userData.permissions || [],
        isActive: userData.isActive
      };

      return adminUser;
    } catch (error: any) {
      console.error('Sign in error:', error);
      
      let errorMessage = 'Login failed. Please try again.';
      
      switch (error.code) {
        case 'auth/user-not-found':
        case 'auth/wrong-password':
          errorMessage = 'Invalid email or password.';
          break;
        case 'auth/user-disabled':
          errorMessage = 'This account has been disabled.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
        case 'auth/network-request-failed':
          errorMessage = 'Network error. Please check your connection.';
          break;
        default:
          if (error.message.includes('Admin privileges required')) {
            errorMessage = 'Access denied: Admin privileges required.';
          } else if (error.message.includes('Account is disabled')) {
            errorMessage = 'Your account has been disabled.';
          } else if (error.message.includes('Admin user data not found')) {
            errorMessage = 'Admin account not found.';
          }
      }
      
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string): Promise<boolean> => {
    try {
      setError(null);
      await sendPasswordResetEmail(firebaseAuth, email);
      return true;
    } catch (error: any) {
      console.error('Password reset error:', error);
      
      let errorMessage = 'Failed to send reset email.';
      
      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many requests. Please try again later.';
          break;
      }
      
      setError(errorMessage);
      return false;
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    loading,
    error,
    signIn,
    resetPassword,
    clearError
  };
}
