import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth, verifyIdToken } from '@/lib/firebase-admin';
import { adminUserService } from '@/lib/firestore';

// Middleware to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header');
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await verifyIdToken(idToken);
    
    // Check if user has admin privileges
    // In production, you would check custom claims
    if (!decodedToken.email_verified) {
      throw new Error('Email not verified');
    }

    return decodedToken;
  } catch {
    throw new Error('Unauthorized');
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    await verifyAdminAuth(request);

    const { searchParams } = new globalThis.URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const role = searchParams.get('role');
    const status = searchParams.get('status');

    // Build query constraints
    const constraints = [];
    if (role) {
      constraints.push(['role', '==', role]);
    }
    if (status === 'active') {
      constraints.push(['isActive', '==', true]);
    } else if (status === 'inactive') {
      constraints.push(['isActive', '==', false]);
    }

    // Get paginated users
    const result = await adminUserService.getPaginated(limit, undefined, constraints);

    return NextResponse.json({
      success: true,
      data: {
        users: result.documents,
        pagination: {
          page,
          limit,
          hasMore: result.hasMore,
          total: result.documents.length,
        }
      }
    });

  } catch (error: unknown) {
    console.error('Admin users API error:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const decodedToken = await verifyAdminAuth(request);

    const body = await request.json();
    const { uid, email, displayName, role, department, permissions } = body;

    // Validate required fields
    if (!uid || !email || !displayName || !role) {
      return NextResponse.json(
        { error: 'Missing required fields: uid, email, displayName, role' },
        { status: 400 }
      );
    }

    // Validate role
    const validRoles = ['super-admin', 'admin', 'editor', 'viewer'];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be one of: ' + validRoles.join(', ') },
        { status: 400 }
      );
    }

    // Create admin user record
    const adminUserData = {
      uid,
      email: email.toLowerCase().trim(),
      displayName: displayName.trim(),
      role,
      permissions: permissions || [],
      isActive: true,
      department: department?.trim() || null,
      notes: '',
    };

    const userId = await adminUserService.create(adminUserData, decodedToken.uid);

    // Set custom claims in Firebase Auth
    const auth = getAdminAuth();
    await auth.setCustomUserClaims(uid, {
      role,
      permissions: permissions || [],
      isAdmin: true,
    });

    return NextResponse.json({
      success: true,
      data: {
        id: userId,
        message: 'Admin user created successfully'
      }
    }, { status: 201 });

  } catch (error: unknown) {
    console.error('Create admin user error:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Verify admin authentication
    const decodedToken = await verifyAdminAuth(request);

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Missing user ID' },
        { status: 400 }
      );
    }

    // Update admin user record
    await adminUserService.update(id, {
      ...updateData,
      updatedBy: decodedToken.uid,
    });

    // Update custom claims if role or permissions changed
    if (updateData.role || updateData.permissions) {
      const user = await adminUserService.getById(id);
      if (user) {
        const auth = getAdminAuth();
        await auth.setCustomUserClaims(user.uid, {
          role: updateData.role || user.role,
          permissions: updateData.permissions || user.permissions,
          isAdmin: true,
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Admin user updated successfully'
    });

  } catch (error: unknown) {
    console.error('Update admin user error:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update admin user' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    await verifyAdminAuth(request);

    const { searchParams } = new globalThis.URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Missing user ID' },
        { status: 400 }
      );
    }

    // Soft delete admin user
    await adminUserService.softDelete(id, decodedToken.uid);

    return NextResponse.json({
      success: true,
      message: 'Admin user deleted successfully'
    });

  } catch (error: unknown) {
    console.error('Delete admin user error:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete admin user' },
      { status: 500 }
    );
  }
}
