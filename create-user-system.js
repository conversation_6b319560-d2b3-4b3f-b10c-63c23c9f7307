/**
 * CREATE COMPLETE USER SYSTEM
 * 
 * Following Firebase principles:
 * - Reference-based architecture
 * - Strategic denormalization
 * - Single source of truth
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Initialize Firebase
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
  private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
  client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
});

const db = admin.firestore();

async function createUserSystem() {
  try {
    console.log('🚀 Creating complete user system following Firebase principles...');
    
    // ========================================
    // 1. CREATE ROLES COLLECTION
    // ========================================
    console.log('📋 Creating roles collection...');
    
    const roles = [
      {
        id: 'super-admin',
        name: 'Super Administrator',
        permissions: ['*'], // All permissions
        level: 100,
        description: 'Full system access',
        canManageUsers: true,
        canManageRoles: true,
        isSystemRole: true
      },
      {
        id: 'admin',
        name: 'Administrator',
        permissions: ['users.view', 'users.create', 'users.edit', 'content.manage'],
        level: 80,
        description: 'System administrator',
        canManageUsers: true,
        canManageRoles: false,
        isSystemRole: true
      },
      {
        id: 'manager',
        name: 'Manager',
        permissions: ['content.manage', 'users.view', 'reports.view'],
        level: 60,
        description: 'Content and user manager',
        canManageUsers: false,
        canManageRoles: false,
        isSystemRole: true
      },
      {
        id: 'editor',
        name: 'Editor',
        permissions: ['content.create', 'content.edit', 'content.view'],
        level: 40,
        description: 'Content editor',
        canManageUsers: false,
        canManageRoles: false,
        isSystemRole: true
      },
      {
        id: 'customer',
        name: 'Customer',
        permissions: ['profile.edit', 'orders.create', 'orders.view'],
        level: 20,
        description: 'Regular customer',
        canManageUsers: false,
        canManageRoles: false,
        isSystemRole: true
      },
      {
        id: 'vendor',
        name: 'Vendor',
        permissions: ['products.manage', 'orders.view', 'profile.edit'],
        level: 30,
        description: 'Product vendor',
        canManageUsers: false,
        canManageRoles: false,
        isSystemRole: true
      }
    ];

    for (const role of roles) {
      await db.collection('roles').doc(role.id).set({
        ...role,
        userCount: 0, // Denormalized count
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        isActive: true
      });
      console.log(`✅ Created role: ${role.name}`);
    }

    // ========================================
    // 2. CREATE USERS COLLECTION
    // ========================================
    console.log('👥 Creating users collection...');
    
    const users = [
      {
        id: 'user-admin-001',
        email: '<EMAIL>',
        name: 'Super Administrator',
        firstName: 'Super',
        lastName: 'Administrator',
        
        // Role information (denormalized for performance)
        role: 'super-admin',
        roleRef: 'roles/super-admin',
        roleName: 'Super Administrator', // Denormalized
        permissions: ['*'], // Cached permissions
        level: 100, // Denormalized for quick comparisons
        
        // Profile data
        profile: {
          adminLevel: 'super',
          department: 'IT',
          title: 'System Administrator',
          bio: 'System administrator with full access'
        },
        
        // Status
        isActive: true,
        isVerified: true,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      },
      {
        id: 'user-admin-002',
        email: '<EMAIL>',
        name: 'Content Manager',
        firstName: 'Content',
        lastName: 'Manager',
        
        // Role information
        role: 'manager',
        roleRef: 'roles/manager',
        roleName: 'Manager',
        permissions: ['content.manage', 'users.view', 'reports.view'],
        level: 60,
        
        // Profile data
        profile: {
          department: 'Content',
          title: 'Content Manager',
          bio: 'Manages content and user activities'
        },
        
        // Status
        isActive: true,
        isVerified: true,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      },
      {
        id: 'user-editor-001',
        email: '<EMAIL>',
        name: 'Content Editor',
        firstName: 'Content',
        lastName: 'Editor',
        
        // Role information
        role: 'editor',
        roleRef: 'roles/editor',
        roleName: 'Editor',
        permissions: ['content.create', 'content.edit', 'content.view'],
        level: 40,
        
        // Profile data
        profile: {
          department: 'Content',
          title: 'Content Editor',
          bio: 'Creates and edits content'
        },
        
        // Status
        isActive: true,
        isVerified: true,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      },
      {
        id: 'user-customer-001',
        email: '<EMAIL>',
        name: 'John Customer',
        firstName: 'John',
        lastName: 'Customer',
        
        // Role information
        role: 'customer',
        roleRef: 'roles/customer',
        roleName: 'Customer',
        permissions: ['profile.edit', 'orders.create', 'orders.view'],
        level: 20,
        
        // Profile data
        profile: {
          customerType: 'regular',
          preferences: {
            notifications: true,
            newsletter: true
          },
          address: {
            street: '123 Main St',
            city: 'Sample City',
            country: 'US'
          }
        },
        
        // Status
        isActive: true,
        isVerified: false,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      }
    ];

    for (const user of users) {
      await db.collection('users').doc(user.id).set(user);
      console.log(`✅ Created user: ${user.name} (${user.role})`);
      
      // Update role user count (denormalized data)
      await db.collection('roles').doc(user.role).update({
        userCount: admin.firestore.FieldValue.increment(1)
      });
    }

    // ========================================
    // 3. CREATE USER PREFERENCES COLLECTION
    // ========================================
    console.log('⚙️  Creating user preferences...');
    
    for (const user of users) {
      const preferences = {
        userId: user.id,
        userRef: `users/${user.id}`,
        
        // UI Preferences
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        
        // Notification Preferences
        notifications: {
          email: true,
          push: true,
          sms: false,
          marketing: user.role === 'customer'
        },
        
        // Privacy Settings
        privacy: {
          profileVisible: user.role !== 'customer',
          showEmail: false,
          allowMessages: true
        },
        
        // Role-specific preferences
        roleSpecific: user.role === 'customer' ? {
          orderNotifications: true,
          promotionalEmails: true
        } : user.role.includes('admin') || user.role === 'manager' ? {
          dashboardLayout: 'grid',
          showAdvancedFeatures: true
        } : {},
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };
      
      await db.collection('user_preferences').doc(user.id).set(preferences);
      console.log(`✅ Created preferences for: ${user.name}`);
    }

    // ========================================
    // 4. CREATE SYSTEM CONFIG
    // ========================================
    console.log('🔧 Creating system configuration...');
    
    await db.collection('system_config').doc('user_system').set({
      initialized: true,
      version: '1.0.0',
      
      // System settings
      settings: {
        allowUserRegistration: true,
        requireEmailVerification: true,
        defaultRole: 'customer',
        maxUsersPerRole: {
          'super-admin': 2,
          'admin': 10,
          'manager': 50,
          'editor': 100,
          'customer': -1, // unlimited
          'vendor': 1000
        }
      },
      
      // Feature flags
      features: {
        roleBasedAccess: true,
        userPreferences: true,
        profileManagement: true,
        bulkOperations: true
      },
      
      // Statistics (denormalized)
      statistics: {
        totalUsers: users.length,
        totalRoles: roles.length,
        activeUsers: users.filter(u => u.isActive).length
      },
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    });

    console.log('✅ System configuration created!');

    // ========================================
    // 5. SUMMARY
    // ========================================
    console.log('');
    console.log('🎉 USER SYSTEM CREATED SUCCESSFULLY!');
    console.log('');
    console.log('📊 COLLECTIONS CREATED:');
    console.log(`✅ roles (${roles.length} documents)`);
    console.log(`✅ users (${users.length} documents)`);
    console.log(`✅ user_preferences (${users.length} documents)`);
    console.log('✅ system_config (1 document)');
    console.log('');
    console.log('👥 USERS CREATED:');
    users.forEach(user => {
      console.log(`✅ ${user.name} (${user.email}) - Role: ${user.roleName}`);
    });
    console.log('');
    console.log('🔐 LOGIN CREDENTIALS:');
    console.log('📧 Super Admin: <EMAIL>');
    console.log('📧 Manager: <EMAIL>');
    console.log('📧 Editor: <EMAIL>');
    console.log('📧 Customer: <EMAIL>');
    console.log('');
    console.log('🔍 CHECK FIREBASE CONSOLE:');
    console.log('https://console.firebase.google.com/project/encreasl-daa43/firestore');
    console.log('');
    console.log('🎯 FIREBASE PRINCIPLES FOLLOWED:');
    console.log('✅ Reference-based architecture (roleRef)');
    console.log('✅ Strategic denormalization (cached permissions, role names)');
    console.log('✅ Single source of truth (users collection)');
    console.log('✅ Parallel data fetching ready');

  } catch (error) {
    console.error('❌ Error creating user system:', error);
  } finally {
    process.exit(0);
  }
}

// Execute the user system creation
createUserSystem();
