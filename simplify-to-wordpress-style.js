/**
 * SIMPLIFY TO WORDPRESS-STYLE ROLES
 * 
 * WordPress-style roles:
 * - admin (full access)
 * - editor (content management)
 * - user (regular user)
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Initialize Firebase
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
  private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
  client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
});

const db = admin.firestore();

async function simplifyToWordPressStyle() {
  try {
    console.log('🚀 Simplifying to WordPress-style roles...');
    
    // ========================================
    // 1. UPDATE ROLES COLLECTION
    // ========================================
    console.log('📋 Updating roles to WordPress-style...');
    
    // Delete all existing roles first
    const existingRoles = await db.collection('roles').get();
    if (!existingRoles.empty) {
      const batch = db.batch();
      existingRoles.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      console.log('🗑️ Deleted old roles');
    }
    
    // Create WordPress-style roles
    const wordpressRoles = [
      {
        id: 'admin',
        name: 'Administrator',
        permissions: ['*'], // All permissions like WordPress admin
        level: 100,
        description: 'Full access to all features (like WordPress admin)',
        canManageUsers: true,
        canManageRoles: true,
        canManageContent: true,
        canManageSettings: true,
        isSystemRole: true
      },
      {
        id: 'editor',
        name: 'Editor',
        permissions: [
          'content.create', 'content.edit', 'content.delete', 'content.publish',
          'media.upload', 'media.edit', 'media.delete',
          'comments.moderate'
        ],
        level: 50,
        description: 'Can manage content and media (like WordPress editor)',
        canManageUsers: false,
        canManageRoles: false,
        canManageContent: true,
        canManageSettings: false,
        isSystemRole: true
      },
      {
        id: 'user',
        name: 'User',
        permissions: [
          'profile.edit', 'profile.view',
          'content.view', 'comments.create'
        ],
        level: 10,
        description: 'Regular user with basic permissions',
        canManageUsers: false,
        canManageRoles: false,
        canManageContent: false,
        canManageSettings: false,
        isSystemRole: true
      }
    ];

    for (const role of wordpressRoles) {
      await db.collection('roles').doc(role.id).set({
        ...role,
        userCount: 0,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        isActive: true
      });
      console.log(`✅ Created WordPress-style role: ${role.name}`);
    }

    // ========================================
    // 2. UPDATE USERS COLLECTION
    // ========================================
    console.log('👥 Updating users to WordPress-style roles...');
    
    // Delete all existing users first
    const existingUsers = await db.collection('users').get();
    if (!existingUsers.empty) {
      const batch = db.batch();
      existingUsers.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      console.log('🗑️ Deleted old users');
    }
    
    // Create WordPress-style users
    const wordpressUsers = [
      {
        id: 'user-admin-001',
        email: '<EMAIL>',
        name: 'Administrator',
        firstName: 'Admin',
        lastName: 'User',
        
        // WordPress-style admin role
        role: 'admin',
        roleRef: 'roles/admin',
        roleName: 'Administrator',
        permissions: ['*'], // All permissions
        level: 100,
        
        // Profile data
        profile: {
          title: 'Site Administrator',
          bio: 'WordPress-style administrator with full access',
          avatar: null
        },
        
        // WordPress-style capabilities
        capabilities: {
          manageOptions: true,
          manageUsers: true,
          editThemes: true,
          editPlugins: true,
          editPosts: true,
          editPages: true,
          editOthersPages: true,
          editOthersPosts: true,
          deletePages: true,
          deletePosts: true,
          manageCategories: true,
          moderateComments: true,
          uploadFiles: true
        },
        
        // Status
        isActive: true,
        isVerified: true,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      },
      {
        id: 'user-editor-001',
        email: '<EMAIL>',
        name: 'Content Editor',
        firstName: 'Content',
        lastName: 'Editor',
        
        // WordPress-style editor role
        role: 'editor',
        roleRef: 'roles/editor',
        roleName: 'Editor',
        permissions: [
          'content.create', 'content.edit', 'content.delete', 'content.publish',
          'media.upload', 'media.edit', 'media.delete',
          'comments.moderate'
        ],
        level: 50,
        
        // Profile data
        profile: {
          title: 'Content Editor',
          bio: 'WordPress-style editor for content management',
          avatar: null
        },
        
        // WordPress-style capabilities
        capabilities: {
          manageOptions: false,
          manageUsers: false,
          editThemes: false,
          editPlugins: false,
          editPosts: true,
          editPages: true,
          editOthersPages: true,
          editOthersPosts: true,
          deletePages: true,
          deletePosts: true,
          manageCategories: true,
          moderateComments: true,
          uploadFiles: true
        },
        
        // Status
        isActive: true,
        isVerified: true,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      },
      {
        id: 'user-regular-001',
        email: '<EMAIL>',
        name: 'Regular User',
        firstName: 'Regular',
        lastName: 'User',
        
        // WordPress-style user role
        role: 'user',
        roleRef: 'roles/user',
        roleName: 'User',
        permissions: [
          'profile.edit', 'profile.view',
          'content.view', 'comments.create'
        ],
        level: 10,
        
        // Profile data
        profile: {
          title: 'Site Member',
          bio: 'Regular site user',
          avatar: null
        },
        
        // WordPress-style capabilities
        capabilities: {
          manageOptions: false,
          manageUsers: false,
          editThemes: false,
          editPlugins: false,
          editPosts: false,
          editPages: false,
          editOthersPages: false,
          editOthersPosts: false,
          deletePages: false,
          deletePosts: false,
          manageCategories: false,
          moderateComments: false,
          uploadFiles: false,
          read: true, // Basic read permission
          editProfile: true
        },
        
        // Status
        isActive: true,
        isVerified: false,
        lastLoginAt: null,
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        createdBy: 'system'
      }
    ];

    for (const user of wordpressUsers) {
      await db.collection('users').doc(user.id).set(user);
      console.log(`✅ Created WordPress-style user: ${user.name} (${user.role})`);
      
      // Update role user count
      await db.collection('roles').doc(user.role).update({
        userCount: admin.firestore.FieldValue.increment(1)
      });
    }

    // ========================================
    // 3. UPDATE USER PREFERENCES
    // ========================================
    console.log('⚙️ Updating user preferences...');
    
    // Delete old preferences
    const existingPrefs = await db.collection('user_preferences').get();
    if (!existingPrefs.empty) {
      const batch = db.batch();
      existingPrefs.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    }
    
    // Create new preferences for WordPress-style users
    for (const user of wordpressUsers) {
      const preferences = {
        userId: user.id,
        userRef: `users/${user.id}`,
        
        // WordPress-style admin preferences
        adminPreferences: user.role === 'admin' ? {
          dashboardLayout: 'grid',
          showWelcomePanel: true,
          showAdvancedMenus: true,
          colorScheme: 'fresh'
        } : null,
        
        // General preferences
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        
        // Notification preferences
        notifications: {
          email: true,
          push: user.role !== 'user', // Admins and editors get push notifications
          comments: user.role === 'admin' || user.role === 'editor',
          updates: user.role === 'admin'
        },
        
        // Privacy settings
        privacy: {
          profileVisible: user.role !== 'user',
          showEmail: false,
          allowMessages: true
        },
        
        // Metadata
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      };
      
      await db.collection('user_preferences').doc(user.id).set(preferences);
      console.log(`✅ Created preferences for: ${user.name}`);
    }

    // ========================================
    // 4. UPDATE SYSTEM CONFIG
    // ========================================
    console.log('🔧 Updating system configuration...');
    
    await db.collection('system_config').doc('user_system').set({
      initialized: true,
      version: '2.0.0',
      style: 'wordpress',
      
      // WordPress-style settings
      settings: {
        allowUserRegistration: true,
        requireEmailVerification: true,
        defaultRole: 'user', // WordPress default
        adminEmail: '<EMAIL>',
        maxUsersPerRole: {
          'admin': 5,     // Limited admins
          'editor': 50,   // Multiple editors
          'user': -1      // Unlimited users
        }
      },
      
      // WordPress-style features
      features: {
        roleBasedAccess: true,
        userManagement: true,
        contentManagement: true,
        mediaLibrary: true,
        commentSystem: true,
        userProfiles: true
      },
      
      // Statistics
      statistics: {
        totalUsers: wordpressUsers.length,
        totalRoles: wordpressRoles.length,
        activeUsers: wordpressUsers.filter(u => u.isActive).length,
        adminUsers: wordpressUsers.filter(u => u.role === 'admin').length,
        editorUsers: wordpressUsers.filter(u => u.role === 'editor').length,
        regularUsers: wordpressUsers.filter(u => u.role === 'user').length
      },
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system'
    });

    console.log('✅ System configuration updated!');

    // ========================================
    // 5. SUMMARY
    // ========================================
    console.log('');
    console.log('🎉 SIMPLIFIED TO WORDPRESS-STYLE SUCCESSFULLY!');
    console.log('');
    console.log('📊 WORDPRESS-STYLE ROLES:');
    wordpressRoles.forEach(role => {
      console.log(`✅ ${role.name} (Level ${role.level}) - ${role.description}`);
    });
    console.log('');
    console.log('👥 WORDPRESS-STYLE USERS:');
    wordpressUsers.forEach(user => {
      console.log(`✅ ${user.name} (${user.email}) - Role: ${user.roleName}`);
    });
    console.log('');
    console.log('🔐 LOGIN CREDENTIALS:');
    console.log('📧 Admin: <EMAIL> (Full access like WordPress admin)');
    console.log('📧 Editor: <EMAIL> (Content management)');
    console.log('📧 User: <EMAIL> (Basic user)');
    console.log('');
    console.log('🎯 WORDPRESS-STYLE FEATURES:');
    console.log('✅ Simple 3-role system (admin, editor, user)');
    console.log('✅ WordPress-like capabilities');
    console.log('✅ Admin has full access (like WordPress admin)');
    console.log('✅ Editor manages content (like WordPress editor)');
    console.log('✅ User has basic permissions');
    console.log('');
    console.log('🔍 CHECK FIREBASE CONSOLE:');
    console.log('https://console.firebase.google.com/project/encreasl-daa43/firestore');

  } catch (error) {
    console.error('❌ Error simplifying to WordPress-style:', error);
  } finally {
    process.exit(0);
  }
}

// Execute the WordPress-style simplification
simplifyToWordPressStyle();
