/**
 * DELETE INCORRECT COLLECTIONS
 * 
 * Deletes the wrong collections and creates only "admins" collection
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Initialize Firebase
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
  private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
  client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
});

const db = admin.firestore();

async function deleteWrongCollections() {
  try {
    console.log('🗑️  Deleting incorrect collections...');
    
    // Collections to delete
    const collectionsToDelete = [
      'admin_audit_logs',
      'admin_permissions', 
      'admin_roles',
      'system_config'
    ];

    for (const collectionName of collectionsToDelete) {
      console.log(`Deleting ${collectionName}...`);
      
      // Get all documents in the collection
      const snapshot = await db.collection(collectionName).get();
      
      if (!snapshot.empty) {
        // Delete all documents in batches
        const batch = db.batch();
        snapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`✅ Deleted ${snapshot.size} documents from ${collectionName}`);
      } else {
        console.log(`⚠️  Collection ${collectionName} is already empty or doesn't exist`);
      }
    }

    console.log('');
    console.log('🚀 Now creating the correct "admins" collection...');
    
    // Create the simple "admins" collection with one admin user
    const adminData = {
      id: 'admin-001',
      email: '<EMAIL>',
      name: 'Super Administrator',
      role: 'super-admin',
      permissions: ['all'],
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };

    await db.collection('admins').doc('admin-001').set(adminData);
    
    console.log('✅ "admins" collection created successfully!');
    console.log('');
    console.log('📊 FINAL RESULT:');
    console.log('✅ Collection: admins');
    console.log('✅ Document: admin-001');
    console.log('✅ Admin Email: <EMAIL>');
    console.log('✅ Role: super-admin');
    console.log('');
    console.log('🔍 Check Firebase Console:');
    console.log('https://console.firebase.google.com/project/encreasl-daa43/firestore');
    console.log('');
    console.log('You should now see ONLY the "admins" collection!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

// Execute the cleanup and creation
deleteWrongCollections();
