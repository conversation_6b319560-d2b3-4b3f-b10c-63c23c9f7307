'use client';

import { useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { firebaseAuth, firebaseDb } from '@/lib/firebase';
import { Shield } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
  requireAdmin?: boolean;
}

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
}

export function AuthGuard({ children, requireAdmin = true }: AuthGuardProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(firebaseAuth, async (user) => {
      try {
        if (!user) {
          // No user signed in, redirect to login
          setAuthenticated(false);
          setAdminUser(null);
          router.push('/login');
          return;
        }

        if (requireAdmin) {
          // Check if user is admin
          const userDoc = await getDoc(doc(firebaseDb, 'users', user.uid));
          
          if (!userDoc.exists()) {
            // User document doesn't exist, sign out and redirect
            await firebaseAuth.signOut();
            router.push('/login');
            return;
          }

          const userData = userDoc.data();
          
          // Verify user is admin and active
          if (userData.role !== 'admin' || !userData.isActive) {
            // Not an admin or inactive, sign out and redirect
            await firebaseAuth.signOut();
            router.push('/login');
            return;
          }

          // User is authenticated admin
          setAdminUser({
            id: userData.id,
            email: userData.email,
            name: userData.name,
            role: userData.role,
            isActive: userData.isActive
          });
        }

        setAuthenticated(true);
      } catch (error) {
        console.error('Auth guard error:', error);
        // On error, sign out and redirect to login
        await firebaseAuth.signOut();
        router.push('/login');
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [router, requireAdmin]);

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
            <Shield className="w-6 h-6 text-white animate-pulse" />
          </div>
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Verifying authentication...</p>
          <p className="text-gray-500 text-sm mt-1">Please wait while we secure your session</p>
        </div>
      </div>
    );
  }

  // If not authenticated, don't render children (will redirect to login)
  if (!authenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-12 h-12 bg-red-600 rounded-xl flex items-center justify-center mx-auto mb-4">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <p className="text-red-600 font-medium">Access Denied</p>
          <p className="text-gray-500 text-sm mt-1">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // User is authenticated, render children
  return <>{children}</>;
}

// Higher-order component for pages that require admin authentication
export function withAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  requireAdmin: boolean = true
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthGuard requireAdmin={requireAdmin}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// Hook to get current admin user (only works inside AuthGuard)
export function useCurrentAdmin() {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(firebaseAuth, async (user) => {
      if (user) {
        try {
          const userDoc = await getDoc(doc(firebaseDb, 'users', user.uid));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            if (userData.role === 'admin' && userData.isActive) {
              setAdminUser({
                id: userData.id,
                email: userData.email,
                name: userData.name,
                role: userData.role,
                isActive: userData.isActive
              });
            }
          }
        } catch (error) {
          console.error('Error fetching admin user:', error);
        }
      } else {
        setAdminUser(null);
      }
    });

    return () => unsubscribe();
  }, []);

  return adminUser;
}
