'use client';

export default function DebugEnvPage() {
  // Check what environment variables are available in the browser
  const envVars = {
    NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    NEXT_PUBLIC_FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    NEXT_PUBLIC_FIREBASE_VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NODE_ENV: process.env.NODE_ENV,
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Debug</h1>
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Available Environment Variables:</h2>
        {Object.entries(envVars).map(([key, value]) => (
          <div key={key} className="flex">
            <span className="font-mono text-sm w-80">{key}:</span>
            <span className="font-mono text-sm text-green-600">
              {value ? `"${value}"` : <span className="text-red-600">undefined</span>}
            </span>
          </div>
        ))}
        
        <div className="mt-6">
          <h2 className="text-lg font-semibold">Test Environment Validation:</h2>
          <button 
            onClick={() => {
              try {
                // Try to import and validate
                import('@/lib/env').then(({ env }) => {
                  console.log('✅ Environment validation successful:', env);
                  alert('Environment validation successful! Check console for details.');
                }).catch((error) => {
                  console.error('❌ Environment validation failed:', error);
                  alert('Environment validation failed! Check console for details.');
                });
              } catch (error) {
                console.error('❌ Import failed:', error);
                alert('Import failed! Check console for details.');
              }
            }}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Test Environment Validation
          </button>
        </div>
      </div>
    </div>
  );
}
