/**
 * Firebase Cloud Messaging Client Integration for Admin App
 * 
 * Enhanced FCM integration with admin-specific features and priority handling.
 */

import { getMessaging, getToken, onMessage, MessagePayload, Messaging } from 'firebase/messaging';
import { firebaseApp } from './firebase';
import { env } from './env';

// FCM instance
let messaging: Messaging | null = null;

// Admin-specific notification settings
const ADMIN_NOTIFICATION_SETTINGS = {
  requireInteraction: true,
  silent: false,
  vibrate: [200, 100, 200],
  badge: '/icons/admin-badge-72x72.png',
  icon: '/icons/admin-icon-192x192.png',
};

// ========================================
// INITIALIZATION
// ========================================

/**
 * Initialize FCM messaging for admin app
 */
export function initializeFCM(): Messaging | null {
  if (typeof window === 'undefined') {
    console.warn('FCM can only be initialized in the browser');
    return null;
  }

  if (!messaging) {
    try {
      messaging = getMessaging(firebaseApp);
      console.log('Admin FCM initialized successfully');
    } catch (error) {
      console.error('Failed to initialize admin FCM:', error);
      return null;
    }
  }

  return messaging;
}

// ========================================
// PERMISSION MANAGEMENT
// ========================================

/**
 * Request notification permission with admin-specific messaging
 */
export async function requestNotificationPermission(): Promise<boolean> {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    console.warn('Notifications not supported in this environment');
    return false;
  }

  try {
    // Show custom permission dialog for admin users
    const userConsent = await showAdminPermissionDialog();
    if (!userConsent) {
      return false;
    }

    const permission = await Notification.requestPermission();
    console.log('Admin notification permission:', permission);
    
    if (permission === 'granted') {
      // Show success message
      showPermissionSuccessMessage();
    }
    
    return permission === 'granted';
  } catch (error) {
    console.error('Error requesting admin notification permission:', error);
    return false;
  }
}

/**
 * Show admin-specific permission dialog
 */
async function showAdminPermissionDialog(): Promise<boolean> {
  return new Promise((resolve) => {
    // Create custom modal for admin permission request
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white rounded-lg p-6 max-w-md mx-4">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
            </svg>
          </div>
          <h3 class="ml-3 text-lg font-medium text-gray-900">Enable Admin Notifications</h3>
        </div>
        <p class="text-sm text-gray-600 mb-6">
          As an admin, you'll receive important notifications about:
          <br>• New contact form submissions
          <br>• System alerts and issues
          <br>• User management updates
          <br>• Campaign status changes
        </p>
        <div class="flex space-x-3">
          <button id="admin-allow-btn" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
            Enable Notifications
          </button>
          <button id="admin-deny-btn" class="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400">
            Not Now
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    const allowBtn = modal.querySelector('#admin-allow-btn');
    const denyBtn = modal.querySelector('#admin-deny-btn');

    allowBtn?.addEventListener('click', () => {
      document.body.removeChild(modal);
      resolve(true);
    });

    denyBtn?.addEventListener('click', () => {
      document.body.removeChild(modal);
      resolve(false);
    });
  });
}

/**
 * Show permission success message
 */
function showPermissionSuccessMessage(): void {
  // Show a toast notification
  const toast = document.createElement('div');
  toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
  toast.innerHTML = `
    <div class="flex items-center">
      <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      Admin notifications enabled successfully!
    </div>
  `;

  document.body.appendChild(toast);

  setTimeout(() => {
    document.body.removeChild(toast);
  }, 3000);
}

/**
 * Check current notification permission status
 */
export function getNotificationPermission(): NotificationPermission {
  if (typeof window === 'undefined' || !('Notification' in window)) {
    return 'default';
  }
  return Notification.permission;
}

/**
 * Check if notifications are supported
 */
export function isNotificationSupported(): boolean {
  return typeof window !== 'undefined' && 'Notification' in window && 'serviceWorker' in navigator;
}

// ========================================
// TOKEN MANAGEMENT
// ========================================

/**
 * Get FCM registration token for admin
 */
export async function getFCMToken(): Promise<string | null> {
  if (!isNotificationSupported()) {
    console.warn('Notifications not supported');
    return null;
  }

  const fcm = initializeFCM();
  if (!fcm) {
    console.error('Admin FCM not initialized');
    return null;
  }

  try {
    // Register admin service worker
    const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
    console.log('Admin service worker registered:', registration);

    // Get token with admin-specific VAPID key
    const token = await getToken(fcm, {
      vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
      serviceWorkerRegistration: registration,
    });

    if (token) {
      console.log('Admin FCM token obtained:', token);
      localStorage.setItem('admin_fcm_token', token);
      return token;
    } else {
      console.log('No admin registration token available');
      return null;
    }
  } catch (error) {
    console.error('Error getting admin FCM token:', error);
    return null;
  }
}

/**
 * Save admin FCM token
 */
export async function saveFCMToken(userId: string, token: string): Promise<boolean> {
  try {
    const response = await fetch('/api/admin/fcm/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await getIdToken()}`,
      },
      body: JSON.stringify({
        userId,
        token,
        platform: 'web',
        isAdmin: true,
        userAgent: navigator.userAgent,
        permissions: ['contact_form', 'system_alerts', 'user_updates'],
      }),
    });

    if (response.ok) {
      console.log('Admin FCM token saved successfully');
      return true;
    } else {
      console.error('Failed to save admin FCM token:', response.statusText);
      return false;
    }
  } catch (error) {
    console.error('Error saving admin FCM token:', error);
    return false;
  }
}

// Helper function to get ID token
async function getIdToken(): Promise<string | null> {
  // This would integrate with your auth system
  // For now, return null or implement based on your auth setup
  return null;
}

// ========================================
// MESSAGE HANDLING
// ========================================

/**
 * Set up foreground message listener for admin
 */
export function onForegroundMessage(callback: (payload: MessagePayload) => void): (() => void) | null {
  const fcm = initializeFCM();
  if (!fcm) {
    console.error('Admin FCM not initialized');
    return null;
  }

  try {
    const unsubscribe = onMessage(fcm, (payload) => {
      console.log('Admin foreground message received:', payload);
      
      // Handle admin-specific message processing
      processAdminMessage(payload);
      
      callback(payload);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error setting up admin foreground message listener:', error);
    return null;
  }
}

/**
 * Process admin-specific messages
 */
function processAdminMessage(payload: MessagePayload): void {
  const messageType = payload.data?.type;
  const priority = payload.data?.priority || 'normal';

  // Log admin message for audit
  console.log(`Admin message received - Type: ${messageType}, Priority: ${priority}`);

  // Handle urgent messages
  if (priority === 'urgent') {
    // Flash the browser tab
    flashBrowserTab();
    
    // Play urgent sound
    playUrgentSound();
    
    // Show persistent notification
    showUrgentNotification(payload);
  }

  // Update admin dashboard if visible
  updateAdminDashboard(messageType, payload.data);
}

/**
 * Flash browser tab for urgent notifications
 */
function flashBrowserTab(): void {
  const originalTitle = document.title;
  let isFlashing = true;
  let flashCount = 0;

  const flashInterval = setInterval(() => {
    if (flashCount >= 10) { // Flash 5 times
      document.title = originalTitle;
      clearInterval(flashInterval);
      return;
    }

    document.title = isFlashing ? '🚨 URGENT ALERT' : originalTitle;
    isFlashing = !isFlashing;
    flashCount++;
  }, 500);
}

/**
 * Play urgent notification sound
 */
function playUrgentSound(): void {
  try {
    const audio = new Audio('/sounds/urgent-notification.mp3');
    audio.volume = 0.7;
    audio.play().catch(error => {
      console.log('Could not play urgent sound:', error);
    });
  } catch (error) {
    console.log('Audio not supported:', error);
  }
}

/**
 * Show urgent notification that requires interaction
 */
function showUrgentNotification(payload: MessagePayload): void {
  if (!isNotificationSupported() || getNotificationPermission() !== 'granted') {
    return;
  }

  const title = `🚨 ${payload.notification?.title || 'URGENT ADMIN ALERT'}`;
  const options: NotificationOptions = {
    ...ADMIN_NOTIFICATION_SETTINGS,
    body: payload.notification?.body || 'Urgent admin attention required',
    tag: 'urgent-admin-alert',
    requireInteraction: true,
    data: payload.data,
  };

  // Trigger vibration separately if supported
  if ('vibrate' in navigator) {
    navigator.vibrate([300, 100, 300, 100, 300]);
  }

  const notification = new Notification(title, options);

  notification.onclick = () => {
    const clickAction = payload.fcmOptions?.link || payload.data?.clickAction || '/admin/dashboard';
    window.focus();
    window.location.href = clickAction;
    notification.close();
  };
}

/**
 * Update admin dashboard with new data
 */
function updateAdminDashboard(messageType: string | undefined, data: any): void {
  // Dispatch custom event for dashboard components to listen to
  const event = new CustomEvent('adminNotificationReceived', {
    detail: { messageType, data }
  });
  window.dispatchEvent(event);
}

// ========================================
// ADMIN TOPIC MANAGEMENT
// ========================================

/**
 * Subscribe to admin-specific topics
 */
export async function subscribeToAdminTopics(): Promise<void> {
  const adminTopics = [
    'admin',
    'admin-contacts',
    'admin-users',
    'admin-campaigns',
    'system-alerts',
    'super-admin', // If user has super admin privileges
  ];

  for (const topic of adminTopics) {
    try {
      await subscribeToTopic(topic);
    } catch (error) {
      console.error(`Failed to subscribe to admin topic ${topic}:`, error);
    }
  }
}

/**
 * Subscribe to FCM topic
 */
export async function subscribeToTopic(topic: string): Promise<boolean> {
  try {
    const response = await fetch('/api/admin/fcm/topics/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${await getIdToken()}`,
      },
      body: JSON.stringify({ topic }),
    });

    if (response.ok) {
      console.log(`Admin subscribed to topic: ${topic}`);
      return true;
    } else {
      console.error(`Failed to subscribe admin to topic ${topic}:`, response.statusText);
      return false;
    }
  } catch (error) {
    console.error(`Error subscribing admin to topic ${topic}:`, error);
    return false;
  }
}

// ========================================
// ADMIN INITIALIZATION
// ========================================

/**
 * Initialize FCM for admin user
 */
export async function initializeFCMForAdmin(userId: string): Promise<string | null> {
  try {
    console.log('Initializing FCM for admin user:', userId);

    // Request permission with admin-specific dialog
    const hasPermission = await requestNotificationPermission();
    if (!hasPermission) {
      console.log('Admin notification permission denied');
      return null;
    }

    // Get admin token
    const token = await getFCMToken();
    if (!token) {
      console.log('Failed to get admin FCM token');
      return null;
    }

    // Save admin token
    const saved = await saveFCMToken(userId, token);
    if (!saved) {
      console.log('Failed to save admin FCM token');
      return null;
    }

    // Subscribe to admin topics
    await subscribeToAdminTopics();

    console.log('Admin FCM initialized successfully for user:', userId);
    return token;
  } catch (error) {
    console.error('Error initializing admin FCM:', error);
    return null;
  }
}

/**
 * Check if admin FCM is properly set up
 */
export function isAdminFCMSetup(): boolean {
  return (
    isNotificationSupported() &&
    getNotificationPermission() === 'granted' &&
    !!localStorage.getItem('admin_fcm_token')
  );
}
