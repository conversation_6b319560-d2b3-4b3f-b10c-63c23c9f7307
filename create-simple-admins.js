/**
 * SIMPLE ADMINS COLLECTION CREATION
 * 
 * Creates exactly what was requested: ONE "admins" collection
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Initialize Firebase
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_ADMIN_PROJECT_ID,
  private_key: process.env.FIREBASE_ADMIN_PRIVATE_KEY,
  client_email: process.env.FIREBASE_ADMIN_CLIENT_EMAIL
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID
});

const db = admin.firestore();

async function createAdminsCollection() {
  try {
    console.log('🚀 Creating "admins" collection...');
    
    // Create a simple admin user document in "admins" collection
    const adminData = {
      id: 'admin-001',
      email: '<EMAIL>',
      name: 'Super Administrator',
      role: 'super-admin',
      permissions: ['all'],
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    };

    // Add to "admins" collection
    await db.collection('admins').doc('admin-001').set(adminData);
    
    console.log('✅ "admins" collection created successfully!');
    console.log('📊 Collection: admins');
    console.log('📄 Document: admin-001');
    console.log('📧 Admin Email: <EMAIL>');
    console.log('');
    console.log('🔍 Check Firebase Console:');
    console.log('https://console.firebase.google.com/project/encreasl-daa43/firestore');

  } catch (error) {
    console.error('❌ Error creating admins collection:', error);
  } finally {
    process.exit(0);
  }
}

// Create the simple admins collection
createAdminsCollection();
