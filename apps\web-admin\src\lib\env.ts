// Simple environment configuration without validation
export const env = {
  // Firebase configuration
  NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID!,
  NEXT_PUBLIC_FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID!,
  NEXT_PUBLIC_FIREBASE_VAPID_KEY: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,

  // App configuration
  NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Encreasl Admin Dashboard',
  NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  NEXT_PUBLIC_REQUIRE_2FA: process.env.NEXT_PUBLIC_REQUIRE_2FA === 'true',
  NEXT_PUBLIC_DEBUG_MODE: process.env.NEXT_PUBLIC_DEBUG_MODE === 'true',
};

// Export commonly used environment variables for easy access
export const {
  NEXT_PUBLIC_APP_NAME,
  NEXT_PUBLIC_APP_VERSION,
  NEXT_PUBLIC_APP_URL,
  NEXT_PUBLIC_FIREBASE_API_KEY,
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  NEXT_PUBLIC_REQUIRE_2FA,
  NEXT_PUBLIC_SESSION_TIMEOUT,
  NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS,
  NEXT_PUBLIC_DEBUG_MODE,
  NEXT_PUBLIC_SHOW_DEV_TOOLS,
} = env;

// Helper functions for common environment checks
export const isProduction = env.NODE_ENV === 'production';
export const isDevelopment = env.NODE_ENV === 'development';
export const isTest = env.NODE_ENV === 'test';

// Firebase configuration object
export const firebaseConfig = {
  apiKey: env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: env.NEXT_PUBLIC_FIREBASE_APP_ID,
  ...(env.NEXT_PUBLIC_FIREBASE_VAPID_KEY && {
    vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
  }),
};

// Admin-specific feature flags
export const adminFeatures = {
  userManagement: env.NEXT_PUBLIC_ENABLE_USER_MANAGEMENT,
  analyticsDashboard: env.NEXT_PUBLIC_ENABLE_ANALYTICS_DASHBOARD,
  contentManagement: env.NEXT_PUBLIC_ENABLE_CONTENT_MANAGEMENT,
  campaignManagement: env.NEXT_PUBLIC_ENABLE_CAMPAIGN_MANAGEMENT,
  debug: env.NEXT_PUBLIC_DEBUG_MODE,
  devTools: env.NEXT_PUBLIC_SHOW_DEV_TOOLS,
};

// Security configuration (client-side only)
export const securityConfig = {
  requireTwoFA: env.NEXT_PUBLIC_REQUIRE_2FA,
  sessionTimeout: env.NEXT_PUBLIC_SESSION_TIMEOUT,
  maxLoginAttempts: env.NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS,
};

// Admin API configuration (client-side only)
export const adminApiConfig = {
  publicBaseUrl: env.NEXT_PUBLIC_ADMIN_API_BASE_URL,
};

// Server-side only environment variables (use with caution)
export function getServerEnv() {
  if (typeof window !== 'undefined') {
    throw new Error('getServerEnv() can only be called on the server side');
  }

  // Import server validation function dynamically to avoid client-side issues
  const { validateAdminServerEnv } = require('@encreasl/env');
  const serverEnv = validateAdminServerEnv();

  return {
    firebaseAdmin: {
      projectId: serverEnv.FIREBASE_ADMIN_PROJECT_ID,
      privateKey: serverEnv.FIREBASE_ADMIN_PRIVATE_KEY,
      clientEmail: serverEnv.FIREBASE_ADMIN_CLIENT_EMAIL,
    },
    database: {
      url: serverEnv.ADMIN_DATABASE_URL,
      poolSize: serverEnv.ADMIN_DATABASE_POOL_SIZE,
    },
    notifications: {
      slackWebhook: serverEnv.ADMIN_SLACK_WEBHOOK_URL,
      discordWebhook: serverEnv.ADMIN_DISCORD_WEBHOOK_URL,
      notificationEmail: serverEnv.ADMIN_NOTIFICATION_EMAIL,
      alertEmail: serverEnv.ADMIN_ALERT_EMAIL,
    },
    security: {
      enableCSP: serverEnv.ADMIN_ENABLE_CSP,
      enableHSTS: serverEnv.ADMIN_ENABLE_HSTS,
    },
    logging: {
      level: serverEnv.ADMIN_LOG_LEVEL,
      enableAuditLogs: serverEnv.ADMIN_ENABLE_AUDIT_LOGS,
    },
  };
}
